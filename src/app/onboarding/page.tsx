"use client";

import {
  Act<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>R<PERSON>,
  CheckCircle2,
  Info,
  Ruler,
  Target,
  TrendingUp,
  User,
} from "lucide-react";
import { type ChangeEvent, type JSX, useEffect, useState } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Type definitions
type Gender = "male" | "female" | "other";
type ActivityLevel = "sedentary" | "lightly_active" | "moderately_active" | "very_active";
type StepNumber = 1 | 2 | 3;

interface FormErrors {
  name?: string;
  age?: string;
  gender?: string;
  height?: string;
  weight?: string;
  bodyFat?: string;
  targetWeight?: string;
  timeline?: string;
}

interface TouchedFields {
  name?: boolean;
  age?: boolean;
  gender?: boolean;
  height?: boolean;
  weight?: boolean;
  bodyFat?: boolean;
  targetWeight?: boolean;
  timeline?: boolean;
}

interface BMICategory {
  label: string;
  color: string;
}

interface ActivityMultipliers {
  sedentary: number;
  lightly_active: number;
  moderately_active: number;
  very_active: number;
}

const Onboarding = () => {
  const [step, setStep] = useState<StepNumber>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<TouchedFields>({});

  // Profile data
  const [name, setName] = useState<string>("");
  const [age, setAge] = useState<string>("");
  const [gender, setGender] = useState<Gender | "">("");
  const [height, setHeight] = useState<string>("");

  // Measurements
  const [weight, setWeight] = useState<string>("");
  const [bodyFat, setBodyFat] = useState<string>("");
  const [waist, setWaist] = useState<string>("");
  const [hip, setHip] = useState<string>("");

  // Goals
  const [targetWeight, setTargetWeight] = useState<string>("");
  const [targetBodyFat, setTargetBodyFat] = useState<string>("");
  const [targetWaist, setTargetWaist] = useState<string>("");
  const [targetHip, setTargetHip] = useState<string>("");
  const [timeline, setTimeline] = useState<string>("");
  const [activityLevel, setActivityLevel] = useState<ActivityLevel | "">("");

  const progress: number = (step / 3) * 100;

  // Real-time validation
  useEffect(() => {
    validateStep();
  }, [name, age, gender, height, weight, targetWeight, timeline, activityLevel, step]);

  const validateStep = (): boolean => {
    const newErrors: FormErrors = {};

    if (step === 1) {
      if (touched.name && !name.trim()) {
        newErrors.name = "Name is required";
      }
      if (touched.name && name.trim().length < 2) {
        newErrors.name = "Name must be at least 2 characters";
      }
      const ageNum = parseInt(age, 10);
      if (touched.age && (!age || ageNum < 13 || ageNum > 120)) {
        newErrors.age = "Please enter a valid age (13-120)";
      }
      const heightNum = parseFloat(height);
      if (touched.height && (!height || heightNum < 100 || heightNum > 250)) {
        newErrors.height = "Please enter a valid height (100-250 cm)";
      }
      if (touched.gender && !gender) {
        newErrors.gender = "Please select your gender";
      }
    }

    if (step === 2) {
      const weightNum = parseFloat(weight);
      if (touched.weight && (!weight || weightNum < 30 || weightNum > 300)) {
        newErrors.weight = "Please enter a valid weight (30-300 kg)";
      }
      const bodyFatNum = parseFloat(bodyFat);
      if (bodyFat && (bodyFatNum < 3 || bodyFatNum > 60)) {
        newErrors.bodyFat = "Body fat should be between 3-60%";
      }
    }

    if (step === 3) {
      const targetWeightNum = parseFloat(targetWeight);
      if (
        touched.targetWeight &&
        (!targetWeight || targetWeightNum < 30 || targetWeightNum > 300)
      ) {
        newErrors.targetWeight = "Please enter a valid target weight";
      }
      const timelineNum = parseInt(timeline, 10);
      if (touched.timeline && (!timeline || timelineNum < 7 || timelineNum > 365)) {
        newErrors.timeline = "Timeline should be between 7-365 days";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleBlur = (field: keyof TouchedFields): void => {
    setTouched((prev) => ({ ...prev, [field]: true }));
  };

  const calculateBMI = (): string => {
    if (!weight || !height) return "0";
    const heightM = parseFloat(height) / 100;
    const weightKg = parseFloat(weight);
    return (weightKg / (heightM * heightM)).toFixed(1);
  };

  const getBMICategory = (bmi: number): BMICategory => {
    if (bmi < 18.5) return { color: "text-blue-600", label: "Underweight" };
    if (bmi < 25) return { color: "text-green-600", label: "Normal" };
    if (bmi < 30) return { color: "text-yellow-600", label: "Overweight" };
    return { color: "text-red-600", label: "Obese" };
  };

  const calculateCalories = (): number => {
    if (!weight || !height || !age || !gender) return 0;

    const weightKg = parseFloat(weight);
    const heightCm = parseFloat(height);
    const ageYears = parseInt(age, 10);

    let bmr: number;
    if (gender === "male") {
      bmr = 88.362 + 13.397 * weightKg + 4.799 * heightCm - 5.677 * ageYears;
    } else {
      bmr = 447.593 + 9.247 * weightKg + 3.098 * heightCm - 4.33 * ageYears;
    }

    const multipliers: ActivityMultipliers = {
      lightly_active: 1.375,
      moderately_active: 1.55,
      sedentary: 1.2,
      very_active: 1.725,
    };

    const multiplier = activityLevel ? multipliers[activityLevel] : 1.2;
    return Math.round(bmr * multiplier);
  };

  const isStepValid = (): boolean => {
    if (step === 1) {
      return Boolean(name && age && gender && height && Object.keys(errors).length === 0);
    }
    if (step === 2) {
      return Boolean(weight && Object.keys(errors).length === 0);
    }
    if (step === 3) {
      return Boolean(targetWeight && timeline && activityLevel && Object.keys(errors).length === 0);
    }
    return false;
  };

  const handleNext = (): void => {
    if (isStepValid()) {
      setStep((prev) => (prev + 1) as StepNumber);
      window.scrollTo({ behavior: "smooth", top: 0 });
    }
  };

  const handleBack = (): void => {
    setStep((prev) => (prev - 1) as StepNumber);
    window.scrollTo({ behavior: "smooth", top: 0 });
  };

  const handleSubmit = async (): Promise<void> => {
    if (!isStepValid()) return;

    setLoading(true);
  };

  const stepIcons: Record<StepNumber, JSX.Element> = {
    1: <User className="w-5 h-5" />,
    2: <Ruler className="w-5 h-5" />,
    3: <Target className="w-5 h-5" />,
  };

  const stepTitles: Record<StepNumber, string> = {
    1: "Personal Details",
    2: "Current Measurements",
    3: "Your Goals",
  };

  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-8 bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5">
      <Card className="w-full max-w-3xl shadow-2xl border-2">
        <CardHeader className="space-y-4 pb-8">
          {/* Step Indicators */}
          <div className="flex justify-between items-center mb-2">
            {([1, 2, 3] as const).map((stepNum) => (
              <div className="flex items-center flex-1" key={stepNum}>
                <div
                  className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
                    stepNum < step
                      ? "bg-primary border-primary text-primary-foreground"
                      : stepNum === step
                        ? "bg-primary border-primary text-primary-foreground ring-4 ring-primary/20"
                        : "bg-background border-muted text-muted-foreground"
                  }`}
                >
                  {stepNum < step ? <CheckCircle2 className="w-6 h-6" /> : stepIcons[stepNum]}
                </div>
                {stepNum < 3 && (
                  <div
                    className={`flex-1 h-1 mx-2 rounded transition-all duration-300 ${
                      stepNum < step ? "bg-primary" : "bg-muted"
                    }`}
                  />
                )}
              </div>
            ))}
          </div>

          <Progress className="h-2.5" value={progress} />

          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
                {stepTitles[step]}
              </CardTitle>
              <CardDescription className="text-base mt-2">
                Step {step} of 3 
              </CardDescription>
            </div>
            <Badge className="text-sm px-3 py-1" variant="outline">
              {step === 1 && "Basic Info"}
              {step === 2 && "Health Metrics"}
              {step === 3 && "Target Setting"}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="pb-8">
          {step === 1 && (
            <div className="space-y-6 animate-in fade-in slide-in-from-right-4 duration-300">
              <Alert className="border-primary/20 bg-primary/5">
                <Info className="h-4 w-4 text-primary" />
                <AlertDescription className="text-sm">
                  This information helps us personalize your fitness journey
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <Label className="text-base font-semibold" htmlFor="name">
                  Full Name <span className="text-destructive">*</span>
                </Label>
                <Input
                  aria-describedby={errors.name ? "name-error" : undefined}
                  aria-invalid={!!errors.name}
                  aria-required="true"
                  className={`h-12 text-base ${errors.name ? "border-destructive focus-visible:ring-destructive" : "border-muted"}`}
                  id="name"
                  onBlur={() => handleBlur("name")}
                  onChange={(e: ChangeEvent<HTMLInputElement>) => setName(e.target.value)}
                  placeholder="e.g., John Doe"
                  value={name}
                />
                {errors.name && (
                  <p
                    className="text-sm text-destructive flex items-center gap-1 animate-in fade-in slide-in-from-top-1"
                    id="name-error"
                  >
                    <Info className="w-3 h-3" />
                    {errors.name}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label className="text-base font-semibold" htmlFor="age">
                    Age <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    aria-invalid={!!errors.age}
                    aria-required="true"
                    className={`h-12 text-base ${errors.age ? "border-destructive" : ""}`}
                    id="age"
                    onBlur={() => handleBlur("age")}
                    onChange={(e: ChangeEvent<HTMLInputElement>) => setAge(e.target.value)}
                    placeholder="e.g., 25"
                    type="number"
                    value={age}
                  />
                  {errors.age && (
                    <p className="text-sm text-destructive flex items-center gap-1 animate-in fade-in">
                      <Info className="w-3 h-3" />
                      {errors.age}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label className="text-base font-semibold" htmlFor="gender">
                    Gender <span className="text-destructive">*</span>
                  </Label>
                  <Select onValueChange={(value: Gender) => setGender(value)} value={gender}>
                    <SelectTrigger
                      aria-required="true"
                      className={`h-12 text-base ${errors.gender ? "border-destructive" : ""}`}
                      id="gender"
                    >
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="female">Female</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-base font-semibold" htmlFor="height">
                  Height (cm) <span className="text-destructive">*</span>
                </Label>
                <Input
                  aria-required="true"
                  className={`h-12 text-base ${errors.height ? "border-destructive" : ""}`}
                  id="height"
                  onBlur={() => handleBlur("height")}
                  onChange={(e: ChangeEvent<HTMLInputElement>) => setHeight(e.target.value)}
                  placeholder="e.g., 175"
                  type="number"
                  value={height}
                />
                {errors.height && (
                  <p className="text-sm text-destructive flex items-center gap-1">
                    <Info className="w-3 h-3" />
                    {errors.height}
                  </p>
                )}
              </div>

              <Button
                className="w-full h-12 text-base font-semibold mt-8"
                disabled={!isStepValid()}
                onClick={handleNext}
                type="button"
              >
                Continue <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-6 animate-in fade-in slide-in-from-right-4 duration-300">
              <Alert className="border-primary/20 bg-primary/5">
                <Activity className="h-4 w-4 text-primary" />
                <AlertDescription className="text-sm">
                  Accurate measurements help us track your progress effectively
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label className="text-base font-semibold" htmlFor="weight">
                    Weight (kg) <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    aria-required="true"
                    className={`h-12 text-base ${errors.weight ? "border-destructive" : ""}`}
                    id="weight"
                    onBlur={() => handleBlur("weight")}
                    onChange={(e: ChangeEvent<HTMLInputElement>) => setWeight(e.target.value)}
                    placeholder="e.g., 70.0"
                    step="0.1"
                    type="number"
                    value={weight}
                  />
                  {errors.weight && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <Info className="w-3 h-3" />
                      {errors.weight}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label
                    className="text-base font-semibold flex items-center gap-2"
                    htmlFor="bodyFat"
                  >
                    Body Fat %
                    <Badge className="text-xs" variant="secondary">
                      Optional
                    </Badge>
                  </Label>
                  <Input
                    className="h-12 text-base"
                    id="bodyFat"
                    onChange={(e: ChangeEvent<HTMLInputElement>) => setBodyFat(e.target.value)}
                    placeholder="e.g., 20.0"
                    step="0.1"
                    type="number"
                    value={bodyFat}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label
                    className="text-base font-semibold flex items-center gap-2"
                    htmlFor="waist"
                  >
                    Waist (cm)
                    <Badge className="text-xs" variant="secondary">
                      Optional
                    </Badge>
                  </Label>
                  <Input
                    className="h-12 text-base"
                    id="waist"
                    onChange={(e: ChangeEvent<HTMLInputElement>) => setWaist(e.target.value)}
                    placeholder="e.g., 80.0"
                    step="0.1"
                    type="number"
                    value={waist}
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-base font-semibold flex items-center gap-2" htmlFor="hip">
                    Hip (cm)
                    <Badge className="text-xs" variant="secondary">
                      Optional
                    </Badge>
                  </Label>
                  <Input
                    className="h-12 text-base"
                    id="hip"
                    onChange={(e: ChangeEvent<HTMLInputElement>) => setHip(e.target.value)}
                    placeholder="e.g., 95.0"
                    step="0.1"
                    type="number"
                    value={hip}
                  />
                </div>
              </div>

              {weight && height && (
                <div className="p-6 bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl border-2 border-primary/20 animate-in fade-in slide-in-from-bottom-2">
                  <div className="flex items-center justify-between mb-3">
                    <p className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                      Your Current BMI
                    </p>
                    <TrendingUp className="w-5 h-5 text-primary" />
                  </div>
                  <div className="flex items-baseline gap-3">
                    <p className="text-5xl font-bold text-primary">{calculateBMI()}</p>
                    <p
                      className={`text-lg font-semibold ${getBMICategory(parseFloat(calculateBMI())).color}`}
                    >
                      {getBMICategory(parseFloat(calculateBMI())).label}
                    </p>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    Based on your height and weight measurements
                  </p>
                </div>
              )}

              <div className="flex gap-3 mt-8">
                <Button
                  className="flex-1 h-12 text-base font-semibold"
                  onClick={handleBack}
                  type="button"
                  variant="outline"
                >
                  <ArrowLeft className="mr-2 w-5 h-5" /> Back
                </Button>
                <Button
                  className="flex-1 h-12 text-base font-semibold"
                  disabled={!isStepValid()}
                  onClick={handleNext}
                  type="button"
                >
                  Continue <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </div>
            </div>
          )}

          {step === 3 && (
            <div className="space-y-6 animate-in fade-in slide-in-from-right-4 duration-300">
              <Alert className="border-primary/20 bg-primary/5">
                <Target className="h-4 w-4 text-primary" />
                <AlertDescription className="text-sm">
                  Set realistic goals to stay motivated throughout your journey
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label className="text-base font-semibold" htmlFor="targetWeight">
                    Target Weight (kg) <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    aria-required="true"
                    className={`h-12 text-base ${errors.targetWeight ? "border-destructive" : ""}`}
                    id="targetWeight"
                    onBlur={() => handleBlur("targetWeight")}
                    onChange={(e: ChangeEvent<HTMLInputElement>) => setTargetWeight(e.target.value)}
                    placeholder="e.g., 65.0"
                    step="0.1"
                    type="number"
                    value={targetWeight}
                  />
                  {weight && targetWeight && (
                    <p className="text-xs text-muted-foreground">
                      {parseFloat(weight) - parseFloat(targetWeight) > 0
                        ? `Goal: Lose ${(parseFloat(weight) - parseFloat(targetWeight)).toFixed(1)} kg`
                        : `Goal: Gain ${(parseFloat(targetWeight) - parseFloat(weight)).toFixed(1)} kg`}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label
                    className="text-base font-semibold flex items-center gap-2"
                    htmlFor="targetBodyFat"
                  >
                    Target Body Fat %
                    <Badge className="text-xs" variant="secondary">
                      Optional
                    </Badge>
                  </Label>
                  <Input
                    className="h-12 text-base"
                    id="targetBodyFat"
                    onChange={(e: ChangeEvent<HTMLInputElement>) =>
                      setTargetBodyFat(e.target.value)
                    }
                    placeholder="e.g., 15.0"
                    step="0.1"
                    type="number"
                    value={targetBodyFat}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label
                    className="text-base font-semibold flex items-center gap-2"
                    htmlFor="targetWaist"
                  >
                    Target Waist (cm)
                    <Badge className="text-xs" variant="secondary">
                      Optional
                    </Badge>
                  </Label>
                  <Input
                    className="h-12 text-base"
                    id="targetWaist"
                    onChange={(e: ChangeEvent<HTMLInputElement>) => setTargetWaist(e.target.value)}
                    placeholder="e.g., 75.0"
                    step="0.1"
                    type="number"
                    value={targetWaist}
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    className="text-base font-semibold flex items-center gap-2"
                    htmlFor="targetHip"
                  >
                    Target Hip (cm)
                    <Badge className="text-xs" variant="secondary">
                      Optional
                    </Badge>
                  </Label>
                  <Input
                    className="h-12 text-base"
                    id="targetHip"
                    onChange={(e: ChangeEvent<HTMLInputElement>) => setTargetHip(e.target.value)}
                    placeholder="e.g., 90.0"
                    step="0.1"
                    type="number"
                    value={targetHip}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-base font-semibold" htmlFor="timeline">
                  Timeline (days) <span className="text-destructive">*</span>
                </Label>
                <Input
                  aria-required="true"
                  className={`h-12 text-base ${errors.timeline ? "border-destructive" : ""}`}
                  id="timeline"
                  onBlur={() => handleBlur("timeline")}
                  onChange={(e: ChangeEvent<HTMLInputElement>) => setTimeline(e.target.value)}
                  placeholder="e.g., 90"
                  type="number"
                  value={timeline}
                />
                {timeline && (
                  <p className="text-xs text-muted-foreground">
                    Approximately {Math.floor(parseInt(timeline) / 7)} weeks or{" "}
                    {Math.floor(parseInt(timeline) / 30)} months
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label className="text-base font-semibold" htmlFor="activityLevel">
                  Activity Level <span className="text-destructive">*</span>
                </Label>
                <Select
                  onValueChange={(value: ActivityLevel) => setActivityLevel(value)}
                  value={activityLevel}
                >
                  <SelectTrigger aria-required="true" className="h-12 text-base" id="activityLevel">
                    <SelectValue placeholder="Select your activity level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sedentary">
                      <div className="py-1">
                        <p className="font-semibold">Sedentary</p>
                        <p className="text-xs text-muted-foreground">Little to no exercise</p>
                      </div>
                    </SelectItem>
                    <SelectItem value="lightly_active">
                      <div className="py-1">
                        <p className="font-semibold">Lightly Active</p>
                        <p className="text-xs text-muted-foreground">Exercise 1-3 days/week</p>
                      </div>
                    </SelectItem>
                    <SelectItem value="moderately_active">
                      <div className="py-1">
                        <p className="font-semibold">Moderately Active</p>
                        <p className="text-xs text-muted-foreground">Exercise 3-5 days/week</p>
                      </div>
                    </SelectItem>
                    <SelectItem value="very_active">
                      <div className="py-1">
                        <p className="font-semibold">Very Active</p>
                        <p className="text-xs text-muted-foreground">Exercise 6-7 days/week</p>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {activityLevel && (
                <div className="p-6 bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl border-2 border-primary/20 animate-in fade-in slide-in-from-bottom-2">
                  <div className="flex items-center justify-between mb-3">
                    <p className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                      Recommended Daily Calories
                    </p>
                    <Activity className="w-5 h-5 text-primary" />
                  </div>
                  <div className="flex items-baseline gap-3">
                    <p className="text-5xl font-bold text-primary">{calculateCalories()}</p>
                    <p className="text-lg font-semibold text-muted-foreground">kcal</p>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    Based on Harris-Benedict equation and your activity level
                  </p>
                </div>
              )}

              <div className="flex gap-3 mt-8">
                <Button
                  className="flex-1 h-12 text-base font-semibold"
                  disabled={loading}
                  onClick={handleBack}
                  type="button"
                  variant="outline"
                >
                  <ArrowLeft className="mr-2 w-5 h-5" /> Back
                </Button>
                <Button
                  className="flex-1 h-12 text-base font-semibold"
                  disabled={loading || !isStepValid()}
                  onClick={handleSubmit}
                  type="button"
                >
                  {loading ? (
                    <>
                      <div className="mr-2 h-5 w-5 animate-spin rounded-full border-2 border-background border-t-transparent" />
                      Creating...
                    </>
                  ) : (
                    <>
                      Complete Setup <CheckCircle2 className="ml-2 w-5 h-5" />
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Onboarding;
